v0.6.2:
  date: 2015-07-22
  changes:
    - Return `undefined` when an unknown extension is provided to prepare and
      the `nothrow` option is specified.
v0.6.1:
  date: 2015-05-22
  changes:
    - Add option for not throwing.
v0.6.0:
  date: 2015-05-20
  changes:
    - Include module name when prepare is successful.
v0.5.0:
  date: 2015-05-20
  changes:
    - Overhaul to support interpret 0.6.0.
v0.3.0:
  date: 2015-01-10
  changes:
    - Breaking: `load` method removed.
    - Improved extension recognition.
    - No longer fails upon dots in filenames.
    - Support confuration objects.
    - Support and test ES6.
    - Support legacy module loading.
v0.2.2:
  date: 2014-12-17
  changes:
    - Expose interpret.
v0.2.0:
  date: 2014-04-20
  changes:
    - Simplify loading of coffee-script and iced-coffee-script.
v0.1.0:
  date: 2014-04-20
  changes:
    - Initial public release.
