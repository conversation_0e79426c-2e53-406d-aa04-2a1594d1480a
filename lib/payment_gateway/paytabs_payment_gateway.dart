import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_paytabs_bridge/BaseBillingShippingInfo.dart';
import 'package:flutter_paytabs_bridge/PaymentSdkConfigurationDetails.dart';
import 'package:flutter_paytabs_bridge/PaymentSdkLocale.dart';
import 'package:flutter_paytabs_bridge/flutter_paytabs_bridge.dart';
import 'package:flutter_paytabs_bridge/IOSThemeConfiguration.dart';
import 'package:flutter_paytabs_bridge/PaymentSDKCardDiscount.dart';

import '../common/config.dart';
import '../common/constants.dart';
import '../screens/checkout/widgets/convet_city_lang.dart';

class PayTabsPaymentGateway {
  static Future<String> processPayment({
    required BuildContext context,
    required Map<String, dynamic> paymentData,
  }) async {
    try {
      final isEng = isEnglish(context);

      // Extract customer data
      final customer = paymentData['customer'] as Map<String, dynamic>;
      final cartItems = paymentData['cartItems'] as List<dynamic>;
      final cartTotal = paymentData['cartTotal'] as double;
      final shippingCost = (paymentData['shipping'] as num?)?.toDouble() ?? 0.0;
      final discountData = paymentData['discountData'] as Map<String, dynamic>?;

      final fullName =
          '${customer['first_name'] ?? ''} ${customer['last_name'] ?? ''}';
      // Configure billing details
      var billingDetails = BillingDetails(
        fullName,
        customer['email'] ?? '',
        customer['phone'] ?? '',
        customer['address'] ?? '',
        'EG',
        // Egypt country code
        'Cairo',
        // Default city
        'New Cairo',
        // Default state
        '11528', // Default zip code
      );

      // Configure shipping details (same as billing for now)
      var shippingDetails = ShippingDetails(
        fullName,
        customer['email'] ?? '',
        customer['phone'] ?? '',
        customer['address'] ?? '',
        'EG',
        'Cairo',
        'New Cairo',
        '11528',
      );

      // Calculate final amount after discounts and add shipping
      double finalAmount = cartTotal;
      if (discountData != null) {
        final discountType = discountData['type'] as String;
        final discountValue = discountData['value'] as num;

        if (discountType == 'pcg') {
          // Percentage discount
          finalAmount = cartTotal * (1 - (discountValue / 100));
        } else {
          // Fixed amount discount
          finalAmount = cartTotal - discountValue;
        }
      }

      // Add shipping cost to final amount
      finalAmount += shippingCost;

      log('PayTabs_Amount: $finalAmount CART_TOTAL: $cartTotal SHIPPING: $shippingCost Discount: $discountData');

      // Ensure amount is not negative
      if (finalAmount < 0) finalAmount = 0;

      // Create cart description with extras
      String cartDescription = cartItems.map((item) {
        String itemDesc = '${item['name']} x${item['quantity']}';
        if (item['extras'] != null && (item['extras'] as List).isNotEmpty) {
          final extrasNames =
              (item['extras'] as List).map((extra) => extra['name']).join(', ');
          itemDesc += ' (+$extrasNames)';
        }
        return itemDesc;
      }).join(', ');

      if (cartDescription.length > 100) {
        cartDescription = '${cartDescription.substring(0, 97)}...';
      }

      // Configure PayTabs payment
      var configuration = PaymentSdkConfigurationDetails(
        profileId: Configurations.payTabsProfileId,
        serverKey: Configurations.payTabsServerKey,
        clientKey: Configurations.payTabsClientKey,
        cartId: DateTime.now().millisecondsSinceEpoch.toString(),
        cartDescription: cartDescription,
        merchantName:
            isIdea2App ? 'Idea2App' : currentVendor?.name ?? 'Idea2App',
        screentTitle: isEng ? 'Pay with Card' : 'الدفع بالبطاقة',
        billingDetails: billingDetails,
        shippingDetails: shippingDetails,
        locale: isEng ? PaymentSdkLocale.EN : PaymentSdkLocale.AR,
        amount: finalAmount,
        currencyCode: 'EGP',
        merchantCountryCode: 'EG',
      );

      // Configure theme
      var theme = IOSThemeConfigurations();

      theme.logoImage = 'assets/images/app_icon.png';
      configuration.iOSThemeConfigurations = theme;

      // Show billing and shipping info
      configuration.showBillingInfo = true;
      configuration.showShippingInfo =
          false; // Set to false as per user preference

      // Create a completer to convert callback to async/await
      final Completer<String> completer = Completer<String>();

      // Start payment
      try {
        await FlutterPaytabsBridge.startCardPayment(configuration, (event) {
          log('EEEEffffff $event');
          if (event['status'] == 'success') {
            // Handle transaction details
            var transactionDetails = event['data'];
            printLog('PayTabs Transaction Details: $transactionDetails');

            if (transactionDetails['isSuccess']) {
              printLog('PayTabs: Successful transaction');
              if (!completer.isCompleted) {
                completer.complete(transactionDetails['transactionReference']);
              }
            } else {
              printLog('PayTabs: Failed transaction');
              if (!completer.isCompleted) {
                completer.completeError('Failed to make online payment!');
              }
            }
          } else if (event['status'] == 'error') {
            // Handle error
            printLog("PayTabs Error: ${event["message"]}");
            if (!completer.isCompleted) {
              completer.completeError('Failed to make online payment!');
            }
          } else if (event['status'] == 'event') {
            // Handle cancel events
            printLog("PayTabs Event: ${event["message"]}");
            if (event['message'] == 'Cancelled' ||
                event['message'] == 'cancel') {
              if (!completer.isCompleted) {
                completer.completeError('Failed to make online payment!');
              }
            }
          }
        });

        // Wait for the payment result
        return await completer.future;
      } catch (e) {
        printLog('PayTabs_Payment_Error: $e');
        if (!completer.isCompleted) {
          completer.completeError('Failed to make online payment!');
        }
        return await completer.future;
      }
    } catch (e) {
      printLog('PayTabs Payment Error: $e');
      throw 'Failed to make online payment!';
    }
  }

  static void cancelPayment() {
    FlutterPaytabsBridge.cancelPayment((_) {
      printLog('PayTabs payment cancelled');
    });
  }
}
