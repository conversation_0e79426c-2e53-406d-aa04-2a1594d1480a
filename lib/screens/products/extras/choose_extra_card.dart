import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../data/boxes.dart';
import '../../../models/vendor/extra_setting_model.dart';
import '../../../models/index.dart';
import '../../../common/tools/price_tools.dart';

class ChooseExtraCard extends StatelessWidget {
  final ExtraSettingsModel? extra;
  final Map<String, dynamic>? extraData;
  final bool isSelected;
  final bool fromCart;

  const ChooseExtraCard({
    super.key,
    this.extra,
    this.extraData,
    this.isSelected = false,
    this.fromCart = false,
  });

  @override
  Widget build(BuildContext context) {
    final currency = Provider.of<AppModel>(context, listen: false).currency;
    final currencyRate =
        Provider.of<AppModel>(context, listen: false).currencyRate;

    // Get data from either extra model or extraData map
    final name = extra?.name ?? extraData?['name'] ?? '';
    final price = extra?.price ?? extraData?['price'];

    if (name.isEmpty && fromCart) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
      margin: const EdgeInsets.symmetric(
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        '$name${price != null && price > 0 ? ' (+${PriceTools.getCurrencyFormatted(price.toString(), currencyRate, currency: currency)})' : ''}',
        style: TextStyle(
          fontSize: fromCart ? 10 : 12,
          color: Colors.grey[700],
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
