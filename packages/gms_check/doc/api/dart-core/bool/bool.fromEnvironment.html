<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no">
  <meta name="description" content="API docs for the bool.fromEnvironment constructor from the Class bool class from the dart:core library, for the Dart programming language.">
  <title>bool.fromEnvironment constructor - bool class - dart:core library - Dart API</title>
  <!-- required because all the links are pseudo-absolute -->
  <base href="../..">

  <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro:500,400i,400,300|Source+Sans+Pro:400,300,700" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="static-assets/github.css">
  <link rel="stylesheet" href="static-assets/styles.css">
  <link rel="icon" href="static-assets/favicon.png">
  <!-- header placeholder -->
</head>

<body>

<div id="overlay-under-drawer"></div>

<header id="title">
  <button id="sidenav-left-toggle" type="button">&nbsp;</button>
  <ol class="breadcrumbs gt-separated dark hidden-xs">
    <li><a href="index.html">gms_check</a></li>
    <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
    <li><a href="dart-core/bool-class.html">bool</a></li>
    <li class="self-crumb">bool.fromEnvironment const constructor</li>
  </ol>
  <div class="self-name">bool.fromEnvironment</div>
  <form class="search navbar-right" role="search">
    <input type="text" id="search-box" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
  </form>
</header>

<main>

  <div id="dartdoc-sidebar-left" class="col-xs-6 col-sm-3 col-md-2 sidebar sidebar-offcanvas-left">
    <header id="header-search-sidebar" class="hidden-l">
      <form class="search-sidebar" role="search">
        <input type="text" id="search-sidebar" autocomplete="off" disabled class="form-control typeahead" placeholder="Loading search...">
      </form>
    </header>
    
    <ol class="breadcrumbs gt-separated dark hidden-l" id="sidebar-nav">
      <li><a href="index.html">gms_check</a></li>
      <li><a href="dart-core/dart-core-library.html">dart:core</a></li>
      <li><a href="dart-core/bool-class.html">bool</a></li>
      <li class="self-crumb">bool.fromEnvironment const constructor</li>
    </ol>
    
    <h5>bool class</h5>
    <ol>
    
      <li class="section-title"><a href="dart-core/bool-class.html#constructors">Constructors</a></li>
      <li><a href="dart-core/bool/bool.fromEnvironment.html">fromEnvironment</a></li>
      <li><a href="dart-core/bool/bool.hasEnvironment.html">hasEnvironment</a></li>
    
      <li class="section-title">
        <a href="dart-core/bool-class.html#instance-properties">Properties</a>
      </li>
      <li><a href="dart-core/bool/hashCode.html">hashCode</a></li>
      <li class="inherited"><a href="dart-core/Object/runtimeType.html">runtimeType</a></li>
    
      <li class="section-title"><a href="dart-core/bool-class.html#instance-methods">Methods</a></li>
      <li><a href="dart-core/bool/toString.html">toString</a></li>
      <li class="inherited"><a href="dart-core/Object/noSuchMethod.html">noSuchMethod</a></li>
    
      <li class="section-title"><a href="dart-core/bool-class.html#operators">Operators</a></li>
      <li><a href="dart-core/bool/operator_bitwise_and.html">operator &</a></li>
      <li><a href="dart-core/bool/operator_bitwise_exclusive_or.html">operator ^</a></li>
      <li><a href="dart-core/bool/operator_bitwise_or.html">operator |</a></li>
      <li class="inherited"><a href="dart-core/Object/operator_equals.html">operator ==</a></li>
    
    
    
    
    </ol>
  </div><!--/.sidebar-offcanvas-left-->

  <div id="dartdoc-main-content" class="col-xs-12 col-sm-9 col-md-8 main-content">
      <div><h1><span class="kind-constructor">bool.fromEnvironment</span> constructor</h1></div>

    <section class="multi-line-signature">
      const
      <span class="name ">bool.fromEnvironment</span>(<wbr><span class="parameter" id="fromEnvironment-param-name"><span class="type-annotation"><a href="dart-core/String-class.html">String</a></span> <span class="parameter-name">name</span>, {</span> <span class="parameter" id="fromEnvironment-param-defaultValue"><span class="type-annotation"><a href="dart-core/bool-class.html">bool</a></span> <span class="parameter-name">defaultValue</span>: <span class="default-value">false</span></span> })
    </section>

    <section class="desc markdown">
      <p>Returns the boolean value of the environment declaration <code>name</code>.</p>
<p>The boolean value of the declaration is <code>true</code> if the declared value is
the string <code>"true"</code>, and <code>false</code> if the value is <code>"false"</code>.</p>
<p>In all other cases, including when there is no declaration for <code>name</code>,
the result is the <code>defaultValue</code>.</p>
<p>The result is the same as would be returned by:</p>
<pre class="language-dart"><code class="language-dart">(const String.fromEnvironment(name) == "true")
    ? true
    : (const String.fromEnvironment(name) == "false")
        ? false
        : defaultValue
</code></pre>
<p>Example:</p>
<pre class="language-dart"><code class="language-dart">const loggingFlag = const bool.fromEnvironment("logging");
</code></pre>
<p>If you want to use a different truth-string than <code>"true"</code>, you can use the
<a href="dart-core/String/String.fromEnvironment.html">String.fromEnvironment</a> constructor directly:</p>
<pre class="language-dart"><code class="language-dart">const isLoggingOn = (const String.fromEnvironment("logging") == "on");
</code></pre>
<p>The string value, or lack of a value, associated with a <code>name</code>
must be consistent across all calls to <a href="dart-core/String/String.fromEnvironment.html">String.fromEnvironment</a>,
<a href="dart-core/int/int.fromEnvironment.html">int.fromEnvironment</a>, <code>bool.fromEnvironment</code> and <a href="dart-core/bool/bool.hasEnvironment.html">bool.hasEnvironment</a>
in a single program.</p>
    </section>
    
    <section class="summary source-code" id="source">
      <h2><span>Implementation</span></h2>
      <pre class="language-dart"><code class="language-dart">&#47;&#47; The .fromEnvironment() constructors are special in that we do not want
&#47;&#47; users to call them using &quot;new&quot;. We prohibit that by giving them bodies
&#47;&#47; that throw, even though const constructors are not allowed to have bodies.
&#47;&#47; Disable those static errors.
&#47;&#47;ignore: const_constructor_with_body
&#47;&#47;ignore: const_factory
external const factory bool.fromEnvironment(String name,
    {bool defaultValue = false});</code></pre>
    </section>

  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-right" class="col-xs-6 col-sm-6 col-md-2 sidebar sidebar-offcanvas-right">
  </div><!--/.sidebar-offcanvas-->

</main>

<footer>
  <span class="no-break">
    gms_check
      1.0.0
  </span>

  <!-- footer-text placeholder -->
</footer>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="static-assets/typeahead.bundle.min.js"></script>
<script src="static-assets/highlight.pack.js"></script>
<script src="static-assets/URI.js"></script>
<script src="static-assets/script.js"></script>
<!-- footer placeholder -->

</body>

</html>
